<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/background_color">

    <!-- Header -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/primary_color"
        android:padding="16dp"
        android:gravity="center_vertical">

        <ImageButton
            android:id="@+id/buttonBack"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_revert"
            android:contentDescription="@string/cancel"
            android:layout_marginEnd="16dp" />

        <TextView
            android:id="@+id/textViewTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/add_note"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="@color/white" />

        <Button
            android:id="@+id/buttonSave"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/save_note"
            android:textColor="@color/white"
            android:background="@android:color/transparent"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- Content -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Title Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="@string/note_title">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="2" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Content Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="@string/note_content">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextContent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:minLines="10"
                    android:gravity="top" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </ScrollView>

</LinearLayout>
