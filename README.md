# تطبيق ملاحظاتي - My Notes App

تطبيق Android بسيط لإدارة الملاحظات الشخصية باللغة العربية.

## المميزات

- ✅ إضافة ملاحظات جديدة
- ✅ تعديل الملاحظات الموجودة
- ✅ حذف الملاحظات
- ✅ عرض قائمة بجميع الملاحظات
- ✅ حفظ البيانات محلياً في قاعدة بيانات SQLite
- ✅ واجهة مستخدم عربية جميلة ومتجاوبة
- ✅ دعم Material Design

## متطلبات التطوير

- Android Studio Arctic Fox أو أحدث
- Android SDK API Level 21 أو أحدث
- Java 8 أو أحدث

## كيفية بناء التطبيق

### 1. فتح المشروع
```bash
# افتح Android Studio
# اختر "Open an existing Android Studio project"
# اختر مجلد المشروع
```

### 2. بناء APK للتطوير (Debug)
```bash
# من خلال Android Studio:
# Build > Build Bundle(s) / APK(s) > Build APK(s)

# أو من خلال سطر الأوامر:
./gradlew assembleDebug
```

### 3. بناء APK للإنتاج (Release)
```bash
# من خلال Android Studio:
# Build > Generate Signed Bundle / APK

# أو من خلال سطر الأوامر:
./gradlew assembleRelease
```

### 4. تثبيت التطبيق على الجهاز
```bash
# تأكد من تفعيل "Developer Options" و "USB Debugging"
# اربط الجهاز بالكمبيوتر
./gradlew installDebug
```

## بنية المشروع

```
app/
├── src/main/
│   ├── java/com/example/mynotes/
│   │   ├── MainActivity.java          # الشاشة الرئيسية
│   │   ├── AddEditNoteActivity.java   # شاشة إضافة/تعديل الملاحظة
│   │   ├── Note.java                  # نموذج البيانات للملاحظة
│   │   ├── DatabaseHelper.java        # مساعد قاعدة البيانات
│   │   └── NotesAdapter.java          # محول قائمة الملاحظات
│   ├── res/
│   │   ├── layout/                    # ملفات التخطيط
│   │   ├── values/                    # الألوان والنصوص والأنماط
│   │   └── mipmap/                    # أيقونات التطبيق
│   └── AndroidManifest.xml           # ملف البيان
└── build.gradle                      # إعدادات البناء
```

## الاستخدام

1. **إضافة ملاحظة جديدة**: اضغط على زر "+" في الشاشة الرئيسية
2. **تعديل ملاحظة**: اضغط على الملاحظة في القائمة
3. **حذف ملاحظة**: اضغط على أيقونة الحذف بجانب الملاحظة
4. **حفظ الملاحظة**: اضغط على زر "حفظ" بعد كتابة المحتوى

## المطور

**عبدالرحمن حسن**

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

## ملاحظات إضافية

- التطبيق يدعم Android 5.0 (API Level 21) وما فوق
- البيانات محفوظة محلياً على الجهاز
- التطبيق لا يحتاج إلى اتصال بالإنترنت
- يمكن تخصيص الألوان والأنماط من ملفات الموارد
