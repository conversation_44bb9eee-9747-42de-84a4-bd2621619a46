<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    android:background="@color/card_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/textViewTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                style="@style/TitleTextStyle"
                android:text="عنوان الملاحظة" />

            <ImageButton
                android:id="@+id/buttonDelete"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@android:drawable/ic_menu_delete"
                android:contentDescription="@string/delete_note"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <TextView
            android:id="@+id/textViewContent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/ContentTextStyle"
            android:text="محتوى الملاحظة..." />

        <TextView
            android:id="@+id/textViewDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="@color/text_secondary"
            android:layout_marginTop="8dp"
            android:gravity="end"
            android:text="تاريخ الإنشاء" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
