<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Base application theme -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/primary_color</item>
        <item name="colorPrimaryDark">@color/primary_dark</item>
        <item name="colorAccent">@color/accent_color</item>
        <item name="android:windowBackground">@color/background_color</item>
    </style>

    <!-- Custom styles for the app -->
    <style name="CardStyle">
        <item name="android:layout_margin">8dp</item>
        <item name="android:background">@color/card_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:padding">16dp</item>
    </style>

    <style name="TitleTextStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:maxLines">2</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="ContentTextStyle">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/text_secondary</item>
        <item name="android:maxLines">3</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginTop">8dp</item>
    </style>

    <style name="ButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:background">@color/primary_color</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">12dp</item>
        <item name="android:layout_margin">8dp</item>
    </style>
</resources>