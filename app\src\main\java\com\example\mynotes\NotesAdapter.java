package com.example.mynotes;

import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class NotesAdapter extends RecyclerView.Adapter<NotesAdapter.NoteViewHolder> {
    private List<Note> notes;
    private Context context;
    private DatabaseHelper databaseHelper;
    private OnNoteDeletedListener onNoteDeletedListener;

    public interface OnNoteDeletedListener {
        void onNoteDeleted();
    }

    public NotesAdapter(Context context, List<Note> notes) {
        this.context = context;
        this.notes = notes;
        this.databaseHelper = new DatabaseHelper(context);
    }

    public void setOnNoteDeletedListener(OnNoteDeletedListener listener) {
        this.onNoteDeletedListener = listener;
    }

    @NonNull
    @Override
    public NoteViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_note, parent, false);
        return new NoteViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NoteViewHolder holder, int position) {
        Note note = notes.get(position);
        holder.textViewTitle.setText(note.getTitle());
        holder.textViewContent.setText(note.getContent());
        holder.textViewDate.setText(note.getFormattedDate());

        // Click listener for editing note
        holder.itemView.setOnClickListener(v -> {
            Intent intent = new Intent(context, AddEditNoteActivity.class);
            intent.putExtra("note_id", note.getId());
            intent.putExtra("note_title", note.getTitle());
            intent.putExtra("note_content", note.getContent());
            intent.putExtra("is_edit", true);
            context.startActivity(intent);
        });

        // Delete button click listener
        holder.buttonDelete.setOnClickListener(v -> {
            showDeleteConfirmationDialog(note, position);
        });
    }

    @Override
    public int getItemCount() {
        return notes.size();
    }

    private void showDeleteConfirmationDialog(Note note, int position) {
        new AlertDialog.Builder(context)
                .setTitle(context.getString(R.string.delete_note))
                .setMessage(context.getString(R.string.delete_confirmation))
                .setPositiveButton(context.getString(R.string.yes), (dialog, which) -> {
                    deleteNote(note, position);
                })
                .setNegativeButton(context.getString(R.string.no), null)
                .show();
    }

    private void deleteNote(Note note, int position) {
        databaseHelper.deleteNote(note.getId());
        notes.remove(position);
        notifyItemRemoved(position);
        notifyItemRangeChanged(position, notes.size());
        
        Toast.makeText(context, context.getString(R.string.note_deleted), Toast.LENGTH_SHORT).show();
        
        if (onNoteDeletedListener != null) {
            onNoteDeletedListener.onNoteDeleted();
        }
    }

    public void updateNotes(List<Note> newNotes) {
        this.notes = newNotes;
        notifyDataSetChanged();
    }

    static class NoteViewHolder extends RecyclerView.ViewHolder {
        TextView textViewTitle, textViewContent, textViewDate;
        ImageButton buttonDelete;

        public NoteViewHolder(@NonNull View itemView) {
            super(itemView);
            textViewTitle = itemView.findViewById(R.id.textViewTitle);
            textViewContent = itemView.findViewById(R.id.textViewContent);
            textViewDate = itemView.findViewById(R.id.textViewDate);
            buttonDelete = itemView.findViewById(R.id.buttonDelete);
        }
    }
}
