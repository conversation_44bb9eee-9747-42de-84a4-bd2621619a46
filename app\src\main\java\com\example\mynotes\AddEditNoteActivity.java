package com.example.mynotes;

import android.os.Bundle;
import android.text.TextUtils;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.textfield.TextInputEditText;

public class AddEditNoteActivity extends AppCompatActivity {
    private TextInputEditText editTextTitle, editTextContent;
    private TextView textViewTitle;
    private Button buttonSave;
    private ImageButton buttonBack;
    
    private DatabaseHelper databaseHelper;
    private boolean isEditMode = false;
    private int noteId = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_add_edit_note);

        initViews();
        initDatabase();
        setupClickListeners();
        handleIntent();
    }

    private void initViews() {
        editTextTitle = findViewById(R.id.editTextTitle);
        editTextContent = findViewById(R.id.editTextContent);
        textViewTitle = findViewById(R.id.textViewTitle);
        buttonSave = findViewById(R.id.buttonSave);
        buttonBack = findViewById(R.id.buttonBack);
    }

    private void initDatabase() {
        databaseHelper = new DatabaseHelper(this);
    }

    private void setupClickListeners() {
        buttonSave.setOnClickListener(v -> saveNote());
        buttonBack.setOnClickListener(v -> finish());
    }

    private void handleIntent() {
        if (getIntent().hasExtra("is_edit") && getIntent().getBooleanExtra("is_edit", false)) {
            isEditMode = true;
            noteId = getIntent().getIntExtra("note_id", -1);
            String title = getIntent().getStringExtra("note_title");
            String content = getIntent().getStringExtra("note_content");
            
            textViewTitle.setText(getString(R.string.edit_note));
            editTextTitle.setText(title);
            editTextContent.setText(content);
        } else {
            textViewTitle.setText(getString(R.string.add_note));
        }
    }

    private void saveNote() {
        String title = editTextTitle.getText().toString().trim();
        String content = editTextContent.getText().toString().trim();

        if (TextUtils.isEmpty(title)) {
            editTextTitle.setError(getString(R.string.title_required));
            editTextTitle.requestFocus();
            return;
        }

        if (TextUtils.isEmpty(content)) {
            editTextContent.setError(getString(R.string.content_required));
            editTextContent.requestFocus();
            return;
        }

        if (isEditMode) {
            // Update existing note
            Note note = new Note(noteId, title, content, System.currentTimeMillis());
            int result = databaseHelper.updateNote(note);
            if (result > 0) {
                Toast.makeText(this, getString(R.string.note_saved), Toast.LENGTH_SHORT).show();
                finish();
            }
        } else {
            // Create new note
            Note note = new Note(title, content);
            long result = databaseHelper.insertNote(note);
            if (result != -1) {
                Toast.makeText(this, getString(R.string.note_saved), Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (databaseHelper != null) {
            databaseHelper.close();
        }
    }
}
